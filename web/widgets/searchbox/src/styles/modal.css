.infini__searchbox--active {
  overflow: hidden !important;
}

.infini__searchbox-modal-container,
.infini__searchbox-modal-container * {
  box-sizing: border-box;
}

.infini__searchbox-modal-container {
  background-color: var(--infini__searchbox-modal-container-background);
  height: 100vh;
  min-height: 590px;
  left: 0;
  position: fixed;
  top: 0;
  width: 100vw;
  z-index: 200;
  display: flex;
  justify-content: center;
  align-items: center;
}

.infini__searchbox-modal {
  display: inline-block;
  position: relative;
  box-shadow: var(--infini__searchbox-modal-shadow);
  border-radius: 12px;
  overflow: hidden;
}

.infini__searchbox-modal > div {
  border-radius: 12px;
  overflow: hidden;
}

@keyframes rotation {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(359deg);
  }
}

@media (max-width: 768px) {
  :root {
    --infini__searchbox-spacing: 10px;
    --infini__searchbox-footer-height: 40px;
  }
  .infini__searchbox-modal {
    height: 100%;
    border-radius: 0;
  }

  .infini__searchbox-modal > div {
    border-radius: 0px;
    overflow: hidden;
  }
}

[hidden] {
  visibility: hidden;
}
