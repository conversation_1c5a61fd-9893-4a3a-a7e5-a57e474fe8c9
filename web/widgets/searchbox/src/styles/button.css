.infini__searchbox-btn {
  align-items: center;
  background: var(--infini__searchbox-searchbox-background);
  border: var(--infini__searchbox-searchbox-border);
  outline: none;
  border-radius: 20px;
  color: var(--infini__searchbox-muted-color);
  cursor: pointer;
  display: flex;
  font-weight: 400;
  height: 40px;
  padding: 0 12px;
  user-select: none;
  gap: 4px;
  width: auto;
  min-width: 42px;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* .infini__searchbox-btn code, kbd, samp, pre {
  font-size: 12px;
} */

.infini__searchbox-btn:hover {
  /* background: var(--infini__searchbox-searchbox-focus-background); */
  box-shadow: var(--infini__searchbox-searchbox-shadow);
  color: var(--infini__searchbox-text-color);
}

.infini__searchbox-btn-icon-container {
  align-items: center;
  display: flex;
}

.infini__searchbox-btn-icon-container .infini__searchbox-btn-icon {
  width: 24px;
  height: 24px;
}

.infini__searchbox-btn-icon-container img {
  width: 24px;
  height: 24px;
}

.infini__searchbox-btn .infini__searchbox-btn-icon {
  color: var(--infini__searchbox-text-color);
}

.infini__searchbox-btn-placeholder {
  font-size: 1rem;
  font-weight: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  text-align: left;
}

.infini__searchbox-btn-keys {
  display: flex;
}

.infini__searchbox-btn-keys > * {
  margin-right: 4px;
}

.infini__searchbox-btn-keys > *:last-child {
  margin-right: 0px;
}

.infini__searchbox-btn-key {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  border: 0px;
  padding: 3px;
  background: var(--infini__searchbox-key-gradient);
  box-shadow: var(--infini__searchbox-key-shadow);
  border: var(--infini__searchbox-key-border);
  color: var(--infini__searchbox-muted-color);
  min-width: 14px;
  height: 14px;
  font-size: 12px;
}

.infini__searchbox-float-btn {
  position: fixed;
  bottom: 32px;
  right: 32px;
  z-index: 150;
  box-shadow:
    0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 9px 28px 8px rgba(0, 0, 0, 0.05);
  border: 0;
  width: auto;
  min-width: 110px;
  max-width: 220px;
  height: 48px;
  border: 2px solid #fff;
  cursor: pointer;
  border-radius: 44px;
  transition: box-shadow 0.3s;
  padding: 0;
  background: linear-gradient(270deg, #F0DCFF, #E1A7FF, #00DBFF);
  -webkit-animation: float-btn-animation 3s ease infinite;
  -moz-animation: float-btn-animation 3s ease infinite;
  animation: float-btn-animation 3s ease infinite;
  transition: .4s ease-in-out;
  background-size: 800% 800%;
  overflow: hidden;
}

@keyframes float-btn-animation {
  0% {
    background-position: 0% 50%
  }

  50% {
    background-position: 100% 50%
  }

  100% {
    background-position: 0% 50%
  }
}

.infini__searchbox-float-btn:hover,
.infini__searchbox-float-btn:active,
.infini__searchbox-float-btn:focus {
  box-shadow:
    0 6px 16px 0 rgba(0, 0, 0, 0.16),
    0 3px 6px -4px rgba(0, 0, 0, 0.24),
    0 9px 28px 8px rgba(0, 0, 0, 0.1);
}

.infini__searchbox-float-btn-container {
  display: flex;
  justify-content: center;
  align-items: center;
  justify-content: space-between;
  gap: 6px;
}

.infini__searchbox-float-btn-icon-container {
  display: flex;
  align-items: center;
}

.infini__searchbox-float-btn-text {
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  padding-left: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.infini__searchbox-float-btn-container img {
  width: 44px;
  height: 44px;
}

@media (max-width: 768px) {
  .infini__searchbox-float-btn {
    min-width: 48px;
    max-width: 48px;
  }
  .infini__searchbox-float-btn-text {
    display: none;
  }
}
