<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0"
    />
  </head>

  <body>
    <div id="searchbox"></div>
    <link rel="stylesheet" href="./index.css">
    <script type="module" >
        import { searchbox } from "./index.js";
        const urlParams = new URLSearchParams(window.location.search);
        const id = urlParams.get('id');
        const token = urlParams.get('token');
        const server = urlParams.get('server');
        searchbox({
          container: "#searchbox",
          id,
          token,
          server,
          linkHref: "./index.css",
        });
    </script>
  </body>
</html>