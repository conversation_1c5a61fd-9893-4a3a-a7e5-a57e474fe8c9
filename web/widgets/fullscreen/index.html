<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0"
    />
  </head>

  <body>
    <div id="fullscreen"></div>
    <link rel="stylesheet" href="./index.css">
    <script type="module" >
        import { fullscreen } from "./index.js";
        const urlParams = new URLSearchParams(window.location.search);
        const id = urlParams.get('id');
        const token = urlParams.get('token');
        const server = urlParams.get('server');
        fullscreen({
          container: "#fullscreen",
          id,
          token,
          server,
          linkHref: "./index.css",
        });
    </script>
  </body>
</html>