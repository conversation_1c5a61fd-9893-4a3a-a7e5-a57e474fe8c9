/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/mufeng889/elegant-router
// Vue auto route: https://github.com/soybeanjs/elegant-router


import type { LazyRouteFunction, RouteObject } from "react-router-dom";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";
type CustomRouteObject = Omit<RouteObject, 'Component'|'index'> & {
  Component?: React.ComponentType<any>|null;
};

export const layouts: Record<RouteLayout, LazyRouteFunction<CustomRouteObject>> = {
  base: () => import("@/layouts/base-layout/index.tsx"),
  blank: () => import("@/layouts/blank-layout/index.tsx"),
};

export const pages: Record<LastLevelRouteKey, LazyRouteFunction<CustomRouteObject>> = {
  403: () => import("@/pages/_builtin/403/index.tsx"),
  404: () => import("@/pages/_builtin/404/index.tsx"),
  500: () => import("@/pages/_builtin/500/index.tsx"),
  "ai-assistant_edit": () => import("@/pages/ai-assistant/edit/[id].tsx"),
  "ai-assistant": () => import("@/pages/ai-assistant/index.tsx"),
  "ai-assistant_list": () => import("@/pages/ai-assistant/list/index.tsx"),
  "ai-assistant_new": () => import("@/pages/ai-assistant/new/index.tsx"),
  "api-token_list": () => import("@/pages/api-token/list/index.tsx"),
  connector_edit: () => import("@/pages/connector/edit/[id].tsx"),
  connector_new: () => import("@/pages/connector/new/index.tsx"),
  "data-source_detail": () => import("@/pages/data-source/detail/[id].tsx"),
  "data-source_edit": () => import("@/pages/data-source/edit/[id].tsx"),
  "data-source_list": () => import("@/pages/data-source/list/index.tsx"),
  "data-source_new-first": () => import("@/pages/data-source/new-first/index.tsx"),
  "data-source_new": () => import("@/pages/data-source/new/index.tsx"),
  guide: () => import("@/pages/guide/index.tsx"),
  home: () => import("@/pages/home/<USER>"),
  integration_edit: () => import("@/pages/integration/edit/[id].tsx"),
  integration_list: () => import("@/pages/integration/list/index.tsx"),
  integration_new: () => import("@/pages/integration/new/index.tsx"),
  login: () => import("@/pages/login/index.tsx"),
  "mcp-server_edit": () => import("@/pages/mcp-server/edit/[id].tsx"),
  "mcp-server_list": () => import("@/pages/mcp-server/list/index.tsx"),
  "mcp-server_new": () => import("@/pages/mcp-server/new/index.tsx"),
  "model-provider_edit": () => import("@/pages/model-provider/edit/[id].tsx"),
  "model-provider_list": () => import("@/pages/model-provider/list/index.tsx"),
  "model-provider_new": () => import("@/pages/model-provider/new/index.tsx"),
  settings: () => import("@/pages/settings/index.tsx"),
};
