const route: App.I18n.Schema['translation']['route'] = {
  403: '无权限',
  404: '页面不存在',
  500: '服务器错误',
  about: '关于',
  'ai-assistant': 'AI 助手',
  'api-token': 'API Token',
  'api-token_list': 'API Token',
  'data-source': '数据源',
  'data-source_edit': '编辑数据源',
  'data-source_list': '数据源',
  'data-source_new': '新增数据源',
  'data-source_new-first': '新增数据源第一步',
  document: '文档',
  document_antd: 'Ant Design 文档',
  document_procomponents: 'ProComponents 文档',
  document_project: '项目文档',
  'document_project-link': '项目文档(外链)',
  document_react: 'React文档',
  document_unocss: 'UnoCSS文档',
  document_vite: 'Vite文档',
  exception: '异常页',
  exception_403: '403',
  exception_404: '404',
  exception_500: '500',
  function: '系统功能',
  'function_event-bus': '事件总线演示',
  'function_hide-child': '隐藏子菜单',
  'function_hide-child_one': '隐藏子菜单',
  'function_hide-child_three': '菜单三',
  'function_hide-child_two': '菜单二',
  'function_multi-tab': '多标签页',
  function_request: '请求',
  'function_super-page': '超级管理员可见',
  function_tab: '标签页',
  'function_toggle-auth': '切换权限',
  guide: '初始化向导',
  home: '首页',
  'iframe-page': '外链页面',
  integration: '嵌入组件',
  integration_edit: '编辑嵌入组件',
  integration_list: '嵌入组件',
  integration_new: '新增嵌入组件',
  login: '登录',
  'login_code-login': '验证码登录',
  'login_pwd-login': '密码登录',
  login_register: ' 注册账号',
  'login_reset-pwd': '重置密码',
  logout: '退出登录',
  manage: '系统管理',
  manage_menu: '菜单管理',
  manage_role: '角色管理',
  manage_user: '用户管理',
  'manage_user-detail': '用户详情',
  'multi-menu': '多级菜单',
  'multi-menu_first': '菜单一',
  'multi-menu_first_child': '菜单一子菜单',
  'multi-menu_second': '菜单二',
  'multi-menu_second_child': '菜单二子菜单',
  'multi-menu_second_child_home': '菜单二子菜单首页',
  'server': '服务端',
  'ai-assistant_list': 'AI 助手',
  'ai-assistant_new': '新增 AI 助手',
  'ai-assistant_edit': '编辑 AI 助手',
  'data-source_detail': '数据源详情',
  'settings': '设置',
  'model-provider': '模型提供商',
  'model-provider_list': '模型提供商',
  'model-provider_new': '新增模型提供商',
  'model-provider_edit': '编辑模型提供商',
  'mcp-server': 'MCP 服务器',
  'mcp-server_list': 'MCP 服务器',
  'mcp-server_new': '新增 MCP 服务器',
  'mcp-server_edit': '编辑 MCP 服务器',
  'user-center': '个人中心'
};

export default route;
