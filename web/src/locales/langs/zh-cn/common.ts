const common: App.I18n.Schema['translation']['common'] = {
  action: '操作',
  add: '新增',
  addSuccess: '添加成功',
  advanced: '高级',
  backToHome: '返回首页',
  batchDelete: '批量删除',
  cancel: '取消',
  check: '勾选',
  close: '关闭',
  columnSetting: '列设置',
  comingSoon: '即将到来...',
  config: '配置',
  confirm: '确认',
  confirmDelete: '确认删除吗？',
  copy: '复制',
  copySuccess: '复制成功',
  create: '创建',
  delete: '删除',
  deleteSuccess: '删除成功',
  edit: '编辑',
  error: '错误',
  errorHint: '出错了，请稍后再试',
  expandColumn: '展开列',
  index: '序号',
  keywordSearch: '请输入关键词搜索',
  loginAgain: '请重新登录！',
  logout: '退出登录',
  logoutConfirm: '确认退出登录吗？',
  lookForward: '敬请期待',
  modify: '修改',
  modifyPassword: '修改密码',
  modifySuccess: '修改成功',
  newPassword: '新密码',
  noData: '无数据',
  oldPassword: '旧密码',
  operate: '操作',
  operation: '操作',
  password: '密码',
  pleaseCheckValue: '请检查输入的值是否合法',
  refresh: '刷新',
  rename: '重命名',
  reset: '重置',
  save: '保存',
  search: '搜索',
  switch: '切换',
  testConnection: '测试连接',
  tip: '提示',
  trigger: '触发',
  tryAgain: '刷新重试',
  update: '更新',
  updateSuccess: '更新成功',
  userCenter: '个人中心',
  warning: '警告',
  yesOrNo: {
    no: '否',
    yes: '是'
  },
  enableOrDisable: {
    enable: '开启',
    disable: '关闭'
  },
  upload: '上传',
  language: {
    en: 'English',
    zh: '简体中文'
  },
  renew_token: '刷新令牌',
  clone: '克隆',
};

export default common;
