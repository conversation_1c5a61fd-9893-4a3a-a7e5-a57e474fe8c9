const route: App.I18n.Schema['translation']['route'] = {
  403: 'No Permission',
  404: 'Page Not Found',
  500: 'Server Error',
  about: 'About',
  'ai-assistant': 'AI Assistant',
  'api-token': 'API Token',
  'api-token_list': 'API Token',
  'data-source': 'Data Source',
  'data-source_edit': 'Edit Data Source',
  'data-source_list': 'Data Source',
  'data-source_new': 'New Data Source',
  'data-source_new-first': 'New Data Source First Step',
  document: 'Document',
  document_antd: 'Ant Design  Document',
  document_procomponents: 'ProComponents Document',
  document_project: 'Project Document',
  'document_project-link': 'Project Document(External Link)',
  document_react: 'React Document',
  document_unocss: 'UnoCSS Document',
  document_vite: 'Vite Document',
  exception: 'Exception',
  exception_403: '403',
  exception_404: '404',
  exception_500: '500',
  function: 'System Function',
  'function_event-bus': 'Event Bus Demo',
  'function_hide-child': 'Hide Child',
  'function_hide-child_one': 'Hide Child',
  'function_hide-child_three': 'Three',
  'function_hide-child_two': 'Two',
  'function_multi-tab': 'Multi Tab',
  function_request: 'Request',
  'function_super-page': 'Super Admin Visible',
  function_tab: 'Tab',
  'function_toggle-auth': 'Toggle Auth',
  guide: 'Guide',
  home: 'Home',
  'iframe-page': 'Iframe',
  integration: 'Integration',
  integration_edit: 'Edit Integration',
  integration_list: 'Integration',
  integration_new: 'New Integration',
  login: 'Login',
  'login_code-login': 'Code Login',
  'login_pwd-login': 'Password Login',
  login_register: 'Register Account',
  'login_reset-pwd': 'Reset Password',
  logout: 'Logout',
  manage: 'System Manage',
  manage_menu: 'Menu Manage',
  manage_role: 'Role Manage',
  manage_user: 'User Manage',
  'manage_user-detail': 'User Detail',
  'multi-menu': 'Multi Menu',
  'multi-menu_first': 'Menu One',
  'multi-menu_first_child': 'Menu One Child',
  'multi-menu_second': 'Menu Two',
  'multi-menu_second_child': 'Menu Two Child',
  'multi-menu_second_child_home': 'Menu Two Child Home',
  'user-center': 'User Center',
  'server': 'Server',
  'ai-assistant_list': 'AI Assistant',
  'ai-assistant_new': 'New AI Assistant',
  'ai-assistant_edit': 'Edit AI Assistant',
  'settings': 'Settings',
  'model-provider': 'LLM Provider',
  'model-provider_list': 'LLM Provider',
  'model-provider_new': 'New LLM Provider',
  'model-provider_edit': 'Edit LLM Provider',
  'mcp-server': 'MCP Server',
  'mcp-server_list': 'MCP Server',
  'mcp-server_new': 'New MCP Server',
  'mcp-server_edit': 'Edit MCP Server',
};

export default route;
