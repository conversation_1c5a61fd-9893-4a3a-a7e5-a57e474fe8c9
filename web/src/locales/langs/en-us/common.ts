const common: App.I18n.Schema['translation']['common'] = {
  action: 'Action',
  add: 'Add',
  addSuccess: 'Add Success',
  advanced: 'Advanced',
  backToHome: 'Back to home',
  batchDelete: 'Batch Delete',
  cancel: 'Cancel',
  check: 'Check',
  close: 'Close',
  columnSetting: 'Column Setting',
  comingSoon: 'Coming soon...',
  config: 'Config',
  confirm: 'Confirm',
  confirmDelete: 'Are you sure you want to delete?',
  copy: 'Copy',
  copySuccess: 'Copy success',
  create: 'Create',
  delete: 'Delete',
  deleteSuccess: 'Delete Success',
  edit: 'Edit',
  error: 'Error',
  errorHint: 'Please try again later',
  expandColumn: 'Expand Column',
  index: 'Index',
  keywordSearch: 'Please enter keyword',
  loginAgain: 'Please login again!',
  logout: 'Logout',
  logoutConfirm: 'Are you sure you want to log out?',
  lookForward: 'Coming soon',
  modify: 'Modify',
  modifyPassword: 'Modify Password',
  modifySuccess: 'Modify Success',
  newPassword: 'New Password',
  noData: 'No Data',
  oldPassword: 'Old Password',
  operate: 'Operate',
  operation: 'Operation',
  password: 'Password',
  pleaseCheckValue: 'Please check whether the value is valid',
  refresh: 'Refresh',
  rename: 'Rename',
  reset: 'Reset',
  save: 'Save',
  search: 'Search',
  switch: 'Switch',
  testConnection: 'Test Connection',
  tip: 'Tip',
  trigger: 'Trigger',
  tryAgain: 'Try Again',
  update: 'Update',
  updateSuccess: 'Update Success',
  userCenter: 'User Center',
  warning: 'Warning',
  yesOrNo: {
    no: 'No',
    yes: 'Yes'
  },
  enableOrDisable: {
    enable: 'Enabled',
    disable: 'Disabled'
  },
  upload: 'Upload',
  language: {
    en: 'English',
    zh: '简体中文',
  },
  renew_token: 'Renew Token',
  clone: 'Clone',
};

export default common;
