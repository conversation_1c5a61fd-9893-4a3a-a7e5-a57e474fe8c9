.datePicker {
  display: flex;
  align-items: center;
  width: 100%;

  .RangeBox {
    display: flex;
    align-items: center;
    :global {
      .ant-btn-group {
        width: 100%;
        display: flex;
      }
      .ant-btn {
        border-color: #d9d9d9;
        color: #666;

        &:hover {
          background-color: #e8f6fe;
        }
      }
      .ant-btn[disabled],
      .ant-btn[disabled]:hover {
        background-color: #f5f5f5;
        color: #bbb;
      }
    }

    .iconBtn {
      width: 32px;

      :global {
        .anticon {
          font-size: 10px;
        }
      }
    }
  }

  .refreshBtn {
    display: flex;
    align-items: center;
    margin-left: 4px !important;
    .play {
      min-width: 32px;
      max-width: 32px;
      padding: 0;
      font-size: 14px;
      color: #1890ff;
    }
  }
}
