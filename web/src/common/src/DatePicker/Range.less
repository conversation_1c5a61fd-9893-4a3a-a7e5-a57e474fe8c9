.rangeBtn {
  flex: 1;
  overflow: hidden;
  padding: 0 8px;

  &:hover {
    background-color: #e8f6fe;
  }

  .rangeContent {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;

    .clock {
      font-size: 14px;
    }

    .label {
      flex: 1;
      text-align: left;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .down {
      font-size: 10px;
    }
  }

  &.minimum {
    width: 48px;

    .rangeContent {
      gap: 0;

      .clock {
        margin-right: 8px;
      }

    }
  }
}

.popover {
  :global {
    .ant-popover-arrow {
      display: none;
    }
    .ant-popover-inner-content {
      padding: 0;
    }
  }
}

.rangeSetting {
  display: flex;

  .menu {
    min-width: 220px;
    .item {
      height: 32px;
      color: #666;
      padding: 0 9px 0 16px;
      cursor: pointer;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &.selected,
      &:hover {
        background-color: #e8f6fe;
      }

      &.disabled {
        color: #dbdbdb;
        background-color: #fff;
        pointer-events: none;
      }

      .icon {
        margin-right: 4px;
        color: #101010;
      }

      .right {
        font-size: 10px;
      }
    }

    .quickSelect {
      padding: 8px 0;
    }

    .setting {
      padding: 8px 0;
      border-top: 1px solid #ebebeb;
    }
  }

  .content {
    border-left: 1px solid #ebebeb;
    width: 500px;
  }
}
