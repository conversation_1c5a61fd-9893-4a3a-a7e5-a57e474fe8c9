export default {
  "datepicker.quick_select.auto_fit": "Auto fit",
  "datepicker.quick_select.today": "Today",
  "datepicker.quick_select.this_week": "This week",
  "datepicker.quick_select.last_15_minutes": "Last 15 minutes",
  "datepicker.quick_select.last_30_minutes": "Last 30 minutes",
  "datepicker.quick_select.last_1_hour": "Last 1 hour",
  "datepicker.quick_select.last_24_hours": "Last 24 hours",
  "datepicker.quick_select.last_7_days": "Last 7 days",
  "datepicker.quick_select.last_30_days": "Last 30 days",
  "datepicker.quick_select.last_90_days": "Last 90 days",
  "datepicker.quick_select": "Quick select",
  "datepicker.quick_select.last": "Last",
  "datepicker.quick_select.next": "Next",
  "datepicker.start_and_end_times": "Start and end times",
  "datepicker.start_and_end_times.start_time": "Start time",
  "datepicker.start_and_end_times.end_time": "End time",
  "datepicker.start_and_end_times.error": "End time must be after start time",
  "datepicker.start_and_end_times.recent": "Recent",
  "datepicker.time_setting": "Time setting",
  "datepicker.time_setting.time_field": "Time field",
  "datepicker.time_setting.time_interval": "Time interval",
  "datepicker.time_setting.time_interval.auto": "Auto",
  "datepicker.time_setting.time_interval.help": "Because of the long time range, time interval can only be calculated automatically.",
  "datepicker.time_setting.time_interval.ms": "Millisecond",
  "datepicker.time_setting.time_interval.s": "Second",
  "datepicker.time_setting.time_interval.m": "Minute",
  "datepicker.time_setting.time_interval.h": "Hour",
  "datepicker.time_setting.time_interval.d": "Day",
  "datepicker.time_setting.time_interval.w": "Week",
  "datepicker.time_setting.time_interval.M": "Month",
  "datepicker.time_setting.time_interval.y": "Year",
  "datepicker.time_setting.timeout": "Timeout",
  "datepicker.time_zone": "Time zone",
  "datepicker.time_zone.current": "Current time zone",
  "datepicker.refresh_every": "Refresh every",
  "datepicker.time.units.s": "seconds",
  "datepicker.time.units.m": "minutes",
  "datepicker.time.units.h": "hours",
  "datepicker.time.units.d": "days",
  "datepicker.time.units.w": "weeks",
  "datepicker.time.units.M": "months",
  "datepicker.time.units.y": "years",
  "datepicker.cancel": "Cancel",
  "datepicker.apply": "Apply",
};
