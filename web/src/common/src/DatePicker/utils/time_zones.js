export const TIMEZONES = [
  {
    value: 'Pacific/Midway',
    label: '(GMT-11:00) Midway Island, Samoa (SST)',
    offset: -11,
    abbrev: 'SST',
    altName: 'Samoa Standard Time',
  },
  {
    value: 'Pacific/Honolulu',
    label: '(GMT-10:00) Hawaii (HAST)',
    offset: -10,
    abbrev: 'HAST',
    altName: 'Hawaii-Aleutian Standard Time',
  },
  {
    value: 'America/Juneau',
    label: '(GMT-8:00) Alaska (AKDT)',
    offset: -8,
    abbrev: 'AKDT',
    altName: 'Alaska Daylight Time',
  },
  { value: 'America/Dawson', label: '(GMT-7:00) Dawson, Yukon ', offset: -7 },
  {
    value: 'America/Phoenix',
    label: '(GMT-7:00) Arizona (MST)',
    offset: -7,
    abbrev: 'MST',
    altName: 'Mountain Standard Time',
  },
  {
    value: 'America/Tijuana',
    label: '(GMT-7:00) Tijuana (PDT)',
    offset: -7,
    abbrev: 'PDT',
    altName: 'Pacific Daylight Time',
  },
  {
    value: 'America/Los_Angeles',
    label: '(GMT-7:00) Pacific Time (PDT)',
    offset: -7,
    abbrev: 'PDT',
    altName: 'Pacific Daylight Time',
  },
  {
    value: 'America/Boise',
    label: '(GMT-6:00) Mountain Time (MDT)',
    offset: -6,
    abbrev: 'MDT',
    altName: 'Mountain Daylight Time',
  },
  {
    value: 'America/Chihuahua',
    label: '(GMT-6:00) Chihuahua, La Paz, Mazatlan ',
    offset: -6,
    abbrev: 'HEPMX',
    altName: 'Mexican Pacific Daylight Time',
  },
  {
    value: 'America/Regina',
    label: '(GMT-6:00) Saskatchewan (CST)',
    offset: -6,
    abbrev: 'CST',
    altName: 'Central Standard Time',
  },
  {
    value: 'America/Belize',
    label: '(GMT-6:00) Central America (CST)',
    offset: -6,
    abbrev: 'CST',
    altName: 'Central Standard Time',
  },
  {
    value: 'America/Chicago',
    label: '(GMT-5:00) Central Time (CDT)',
    offset: -5,
    abbrev: 'CDT',
    altName: 'Central Daylight Time',
  },
  {
    value: 'America/Mexico_City',
    label: '(GMT-5:00) Guadalajara, Mexico City, Monterrey (CDT)',
    offset: -5,
    abbrev: 'CDT',
    altName: 'Central Daylight Time',
  },
  {
    value: 'America/Bogota',
    label: '(GMT-5:00) Bogota, Lima, Quito (COT)',
    offset: -5,
    abbrev: 'COT',
    altName: 'Colombia Standard Time',
  },
  {
    value: 'America/Lima',
    label: '(GMT-5:00) Pittsburgh (PET)',
    offset: -5,
    abbrev: 'PET',
    altName: 'Peru Standard Time',
  },
  {
    value: 'America/Detroit',
    label: '(GMT-4:00) Eastern Time (EDT)',
    offset: -4,
    abbrev: 'EDT',
    altName: 'Eastern Daylight Time',
  },
  {
    value: 'America/Caracas',
    label: '(GMT-4:00) Caracas, La Paz (VET)',
    offset: -4,
    abbrev: 'VET',
    altName: 'Venezuela Time',
  },
  {
    value: 'America/Santiago',
    label: '(GMT-4:00) Santiago (CLT)',
    offset: -4,
    abbrev: 'CLT',
    altName: 'Chile Standard Time',
  },
  {
    value: 'America/Sao_Paulo',
    label: '(GMT-3:00) Brasilia (BRT)',
    offset: -3,
    abbrev: 'BRT',
    altName: 'Brasilia Standard Time',
  },
  {
    value: 'America/Montevideo',
    label: '(GMT-3:00) Montevideo (UYT)',
    offset: -3,
    abbrev: 'UYT',
    altName: 'Uruguay Standard Time',
  },
  {
    value: 'America/Argentina/Buenos_Aires',
    label: '(GMT-3:00) Buenos Aires, Georgetown ',
    offset: -3,
    abbrev: 'America/Argentina/Buenos_Aires',
    altName: 'America/Argentina/Buenos_Aires',
  },
  {
    value: 'America/St_Johns',
    label: '(GMT-2:30) Newfoundland and Labrador (HETN)',
    offset: -2.5,
    abbrev: 'HETN',
    altName: 'Newfoundland Daylight Time',
  },
  { value: 'America/Godthab', label: '(GMT-2:00) Greenland ', offset: -2 },
  {
    value: 'Atlantic/Cape_Verde',
    label: '(GMT-1:00) Cape Verde Islands (CVT)',
    offset: -1,
    abbrev: 'CVT',
    altName: 'Cape Verde Standard Time',
  },
  {
    value: 'Atlantic/Azores',
    label: '(GMT+0:00) Azores ',
    offset: 0,
    abbrev: 'AZOST',
    altName: 'Azores Summer Time',
  },
  {
    value: 'Etc/GMT',
    label: '(GMT+0:00) UTC (GMT)',
    offset: 0,
    abbrev: 'GMT',
    altName: 'ETC/GMT',
  },
  {
    value: 'Africa/Casablanca',
    label: '(GMT+0:00) Casablanca, Monrovia (WET)',
    offset: 0,
    abbrev: 'WET',
    altName: 'Western European Standard Time',
  },
  {
    value: 'Europe/London',
    label: '(GMT+1:00) Edinburgh, London (BST)',
    offset: 1,
    abbrev: 'BST',
    altName: 'British Summer Time',
  },
  {
    value: 'Europe/Dublin',
    label: '(GMT+1:00) Dublin (BST)',
    offset: 1,
    abbrev: 'BST',
    altName: 'British Summer Time',
  },
  {
    value: 'Europe/Lisbon',
    label: '(GMT+1:00) Lisbon (WEST)',
    offset: 1,
    abbrev: 'WEST',
    altName: 'Western European Summer Time',
  },
  {
    value: 'Atlantic/Canary',
    label: '(GMT+1:00) Canary Islands (WEST)',
    offset: 1,
    abbrev: 'WEST',
    altName: 'Western European Summer Time',
  },
  {
    value: 'Africa/Algiers',
    label: '(GMT+1:00) West Central Africa (CET)',
    offset: 1,
    abbrev: 'CET',
    altName: 'Central European Standard Time',
  },
  {
    value: 'Europe/Belgrade',
    label: '(GMT+2:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague (CEST)',
    offset: 2,
    abbrev: 'CEST',
    altName: 'Central European Summer Time',
  },
  {
    value: 'Europe/Sarajevo',
    label: '(GMT+2:00) Sarajevo, Skopje, Warsaw, Zagreb (CEST)',
    offset: 2,
    abbrev: 'CEST',
    altName: 'Central European Summer Time',
  },
  {
    value: 'Europe/Brussels',
    label: '(GMT+2:00) Brussels, Copenhagen, Madrid, Paris (CEST)',
    offset: 2,
    abbrev: 'CEST',
    altName: 'Central European Summer Time',
  },
  {
    value: 'Europe/Amsterdam',
    label: '(GMT+2:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna (CEST)',
    offset: 2,
    abbrev: 'CEST',
    altName: 'Central European Summer Time',
  },
  {
    value: 'Africa/Cairo',
    label: '(GMT+2:00) Cairo (EET)',
    offset: 2,
    abbrev: 'EET',
    altName: 'Eastern European Standard Time',
  },
  {
    value: 'Africa/Harare',
    label: '(GMT+2:00) Harare, Pretoria (CAT)',
    offset: 2,
    abbrev: 'CAT',
    altName: 'Central Africa Time',
  },
  {
    value: 'Europe/Berlin',
    label: '(GMT+2:00) Frankfurt (CEST)',
    offset: 2,
    abbrev: 'CEST',
    altName: 'Central European Summer Time',
  },
  {
    value: 'Europe/Bucharest',
    label: '(GMT+3:00) Bucharest (EEST)',
    offset: 3,
    abbrev: 'EEST',
    altName: 'Eastern European Summer Time',
  },
  {
    value: 'Europe/Helsinki',
    label: '(GMT+3:00) Helsinki, Kiev, Riga, Sofia, Tallinn, Vilnius (EEST)',
    offset: 3,
    abbrev: 'EEST',
    altName: 'Eastern European Summer Time',
  },
  {
    value: 'Europe/Athens',
    label: '(GMT+3:00) Athens, Minsk (EEST)',
    offset: 3,
    abbrev: 'EEST',
    altName: 'Eastern European Summer Time',
  },
  {
    value: 'Asia/Jerusalem',
    label: '(GMT+3:00) Jerusalem ',
    offset: 3,
    altName: 'Israel Daylight Time',
  },
  {
    value: 'Europe/Moscow',
    label: '(GMT+3:00) Istanbul, Moscow, St. Petersburg, Volgograd (MSK)',
    offset: 3,
    abbrev: 'MSK',
    altName: 'Moscow Standard Time',
  },
  {
    value: 'Asia/Kuwait',
    label: '(GMT+3:00) Kuwait, Riyadh (AST)',
    offset: 3,
    abbrev: 'AST',
    altName: 'Arabian Standard Time',
  },
  {
    value: 'Africa/Nairobi',
    label: '(GMT+3:00) Nairobi (EAT)',
    offset: 3,
    abbrev: 'EAT',
    altName: 'East Africa Time',
  },
  {
    value: 'Asia/Baghdad',
    label: '(GMT+3:00) Baghdad (AST)',
    offset: 3,
    abbrev: 'AST',
    altName: 'Arabian Standard Time',
  },
  {
    value: 'Asia/Dubai',
    label: '(GMT+4:00) Abu Dhabi, Muscat (GST)',
    offset: 4,
    abbrev: 'GST',
    altName: 'Gulf Standard Time',
  },
  {
    value: 'Asia/Baku',
    label: '(GMT+4:00) Baku, Tbilisi, Yerevan (AZT)',
    offset: 4,
    abbrev: 'AZT',
    altName: 'Azerbaijan Standard Time',
  },
  {
    value: 'Asia/Tehran',
    label: '(GMT+4:30) Tehran (IRDT)',
    offset: 4.5,
    abbrev: 'IRDT',
    altName: 'Iran Daylight Time',
  },
  {
    value: 'Asia/Kabul',
    label: '(GMT+4:30) Kabul (AFT)',
    offset: 4.5,
    abbrev: 'AFT',
    altName: 'Afghanistan Time',
  },
  {
    value: 'Asia/Yekaterinburg',
    label: '(GMT+5:00) Ekaterinburg (YEKT)',
    offset: 5,
    abbrev: 'YEKT',
    altName: 'Yekaterinburg Standard Time',
  },
  {
    value: 'Asia/Karachi',
    label: '(GMT+5:00) Islamabad, Karachi, Tashkent (PKT)',
    offset: 5,
    abbrev: 'PKT',
    altName: 'Pakistan Standard Time',
  },
  {
    value: 'Asia/Kolkata',
    label: '(GMT+5:30) Chennai, Kolkata, Mumbai, New Delhi (IST)',
    offset: 5.5,
    abbrev: 'IST',
    altName: 'India Standard Time',
  },
  {
    value: 'Asia/Colombo',
    label: '(GMT+5:30) Sri Jayawardenepura (IST)',
    offset: 5.5,
    abbrev: 'IST',
    altName: 'India Standard Time',
  },
  {
    value: 'Asia/Kathmandu',
    label: '(GMT+5:45) Kathmandu ',
    offset: 5.75,
    abbrev: 'UTC+5.75',
    altName: 'Kathmandu Time',
  },
  {
    value: 'Asia/Dhaka',
    label: '(GMT+6:00) Astana, Dhaka (BST)',
    offset: 6,
    abbrev: 'BST',
    altName: 'Bangladesh Standard Time',
  },
  {
    value: 'Asia/Almaty',
    label: '(GMT+6:00) Almaty, Novosibirsk (ALMT)',
    offset: 6,
    abbrev: 'ALMT',
    altName: 'East Kazakhstan Time',
  },
  {
    value: 'Asia/Rangoon',
    label: '(GMT+6:30) Yangon Rangoon ',
    offset: 6.5,
    abbrev: 'Asia/Yangon',
    altName: 'Asia/Yangon',
  },
  {
    value: 'Asia/Bangkok',
    label: '(GMT+7:00) Bangkok, Hanoi, Jakarta (ICT)',
    offset: 7,
    abbrev: 'ICT',
    altName: 'Indochina Time',
  },
  {
    value: 'Asia/Krasnoyarsk',
    label: '(GMT+7:00) Krasnoyarsk (KRAT)',
    offset: 7,
    abbrev: 'KRAT',
    altName: 'Krasnoyarsk Standard Time',
  },
  {
    value: 'Asia/Shanghai',
    label: '(GMT+8:00) Beijing, Chongqing, Hong Kong SAR, Urumqi (CST)',
    offset: 8,
    abbrev: 'CST',
    altName: 'China Standard Time',
  },
  {
    value: 'Asia/Kuala_Lumpur',
    label: '(GMT+8:00) Kuala Lumpur, Singapore (MYT)',
    offset: 8,
    abbrev: 'MYT',
    altName: 'Malaysia Time',
  },
  {
    value: 'Asia/Taipei',
    label: '(GMT+8:00) Taipei (CST)',
    offset: 8,
    abbrev: 'CST',
    altName: 'Taipei Standard Time',
  },
  {
    value: 'Australia/Perth',
    label: '(GMT+8:00) Perth (AWST)',
    offset: 8,
    abbrev: 'AWST',
    altName: 'Australian Western Standard Time',
  },
  {
    value: 'Asia/Irkutsk',
    label: '(GMT+8:00) Irkutsk, Ulaanbaatar (IRKT)',
    offset: 8,
    abbrev: 'IRKT',
    altName: 'Irkutsk Standard Time',
  },
  {
    value: 'Asia/Seoul',
    label: '(GMT+9:00) Seoul (KST)',
    offset: 9,
    abbrev: 'KST',
    altName: 'Korean Standard Time',
  },
  {
    value: 'Asia/Tokyo',
    label: '(GMT+9:00) Osaka, Sapporo, Tokyo (JST)',
    offset: 9,
    abbrev: 'JST',
    altName: 'Japan Standard Time',
  },
  {
    value: 'Australia/Darwin',
    label: '(GMT+9:30) Darwin (ACST)',
    offset: 9.5,
    abbrev: 'ACST',
    altName: 'Australian Central Standard Time',
  },
  {
    value: 'Australia/Adelaide',
    label: '(GMT+9:30) Adelaide (ACST)',
    offset: 9.5,
    abbrev: 'ACST',
    altName: 'Australian Central Standard Time',
  },
  {
    value: 'Asia/Yakutsk',
    label: '(GMT+10:00) Yakutsk (YAKT)',
    offset: 10,
    abbrev: 'YAKT',
    altName: 'Yakutsk Standard Time',
  },
  {
    value: 'Australia/Sydney',
    label: '(GMT+10:00) Canberra, Melbourne, Sydney (AEST)',
    offset: 10,
    abbrev: 'AEST',
    altName: 'Australian Eastern Standard Time',
  },
  {
    value: 'Australia/Brisbane',
    label: '(GMT+10:00) Brisbane (AEST)',
    offset: 10,
    abbrev: 'AEST',
    altName: 'Australian Eastern Standard Time',
  },
  {
    value: 'Australia/Hobart',
    label: '(GMT+10:00) Hobart (AEST)',
    offset: 10,
    abbrev: 'AEST',
    altName: 'Australian Eastern Standard Time',
  },
  {
    value: 'Asia/Vladivostok',
    label: '(GMT+10:00) Vladivostok (VLAT)',
    offset: 10,
    abbrev: 'VLAT',
    altName: 'Vladivostok Standard Time',
  },
  {
    value: 'Pacific/Guam',
    label: '(GMT+10:00) Guam, Port Moresby (ChST)',
    offset: 10,
    abbrev: 'ChST',
    altName: 'Chamorro Standard Time',
  },
  {
    value: 'Asia/Magadan',
    label: '(GMT+11:00) Magadan, Solomon Islands, New Caledonia (MAGT)',
    offset: 11,
    abbrev: 'MAGT',
    altName: 'Magadan Standard Time',
  },
  {
    value: 'Asia/Kamchatka',
    label: '(GMT+12:00) Kamchatka, Marshall Islands (PETT)',
    offset: 12,
    abbrev: 'PETT',
    altName: 'Petropavlovsk-Kamchatski Standard Time',
  },
  {
    value: 'Pacific/Fiji',
    label: '(GMT+12:00) Fiji Islands (FJT)',
    offset: 12,
    abbrev: 'FJT',
    altName: 'Fiji Standard Time',
  },
  {
    value: 'Pacific/Auckland',
    label: '(GMT+12:00) Auckland, Wellington (NZST)',
    offset: 12,
    abbrev: 'NZST',
    altName: 'New Zealand Standard Time',
  },
  {
    value: 'Pacific/Tongatapu',
    label: "(GMT+13:00) Nuku'alofa (TOT)",
    offset: 13,
    abbrev: 'TOT',
    altName: 'Tonga Standard Time',
  },
];
