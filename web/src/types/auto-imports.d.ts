/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const AButton: typeof import('antd')['Button']
  const ACard: typeof import('antd')['Card']
  const ACheckbox: typeof import('antd')['Checkbox']
  const ACol: typeof import('antd')['Col']
  const ACollapse: typeof import('antd')['Collapse']
  const ACollapseItem: typeof import('antd')['CollapseItem']
  const AColorPicker: typeof import('antd')['ColorPicker']
  const AConfigProvider: typeof import('antd')['ConfigProvider']
  const ADVANCED: typeof import('antd')['DVANCED']
  const ADescriptions: typeof import('antd')['Descriptions']
  const ADivider: typeof import('antd')['Divider']
  const ADrawer: typeof import('antd')['Drawer']
  const AEmpty: typeof import('antd')['Empty']
  const AFlex: typeof import('antd')['Flex']
  const AForm: typeof import('antd')['Form']
  const AGPL: typeof import('../components/License/AGPL.jsx')['default']
  const AInput: typeof import('antd')['Input']
  const AInputNumber: typeof import('antd')['InputNumber']
  const AList: typeof import('antd')['List']
  const AMenu: typeof import('antd')['Menu']
  const AModal: typeof import('antd')['Modal']
  const APP_DOMAIN: typeof import('antd')['PP_DOMAIN']
  const APopconfirm: typeof import('antd')['Popconfirm']
  const APopover: typeof import('antd')['Popover']
  const ARadio: typeof import('antd')['Radio']
  const ARow: typeof import('antd')['Row']
  const ASelect: typeof import('antd')['Select']
  const ASpace: typeof import('antd')['Space']
  const AStatistic: typeof import('antd')['Statistic']
  const ASwitch: typeof import('antd')['Switch']
  const ATable: typeof import('antd')['Table']
  const ATag: typeof import('antd')['Tag']
  const ATooltip: typeof import('antd')['Tooltip']
  const ATree: typeof import('antd')['Tree']
  const ATypography: typeof import('antd')['Typography']
  const AWatermark: typeof import('antd')['Watermark']
  const AppProvider: typeof import('../components/stateful/AppProvider')['default']
  const BetterScroll: typeof import('../components/stateless/custom/BetterScroll')['default']
  const BeyondHiding: typeof import('../components/stateless/custom/BeyondHiding')['default']
  const BucketSVG: typeof import('../components/icons/index.jsx')['BucketSVG']
  const ButtonIcon: typeof import('../components/stateless/custom/ButtonIcon')['default']
  const ButtonRadio: typeof import('../components/common/ButtonRadio')['default']
  const ConnectorImageIcon: typeof import('../components/icons/connector.jsx')['ConnectorImageIcon']
  const DATE_FORMAT: typeof import('../components/License/index.jsx')['DATE_FORMAT']
  const DEFAULT_WORKTIME_VALUE: typeof import('../components/datasource/work_time/index.jsx')['DEFAULT_WORKTIME_VALUE']
  const DarkModeContainer: typeof import('../components/stateless/common/DarkModeContainer')['default']
  const DarkSystemLogo: typeof import('../components/stateless/common/DarkSystemLogo')['default']
  const DataSync: typeof import('../components/datasource/data_sync/index.jsx')['DataSync']
  const DragContent: typeof import('../components/advanced/DragContent')['default']
  const DropdownList: typeof import('../components/common/DropdownList')['default']
  const DynamicImageIcon: typeof import('../components/icons/dynamic.jsx')['DynamicImageIcon']
  const ExceptionBase: typeof import('../components/stateless/common/ExceptionBase')['default']
  const FullScreen: typeof import('../components/stateless/common/FullScreen')['default']
  const GlobalLoading: typeof import('../components/stateless/common/GlobalLoading')['default']
  const GoogleDriveSVG: typeof import('../components/icons/index.jsx')['GoogleDriveSVG']
  const HugoSVG: typeof import('../components/icons/index.jsx')['HugoSVG']
  const IconAntDesignEnterOutlined: typeof import('~icons/ant-design/enter-outlined.tsx')['default']
  const IconAntDesignInboxOutlined: typeof import('~icons/ant-design/inbox-outlined.tsx')['default']
  const IconAntDesignReloadOutlined: typeof import('~icons/ant-design/reload-outlined.tsx')['default']
  const IconAntDesignSendOutlined: typeof import('~icons/ant-design/send-outlined.tsx')['default']
  const IconAntDesignSettingOutlined: typeof import('~icons/ant-design/setting-outlined.tsx')['default']
  const IconCarbonAdd: typeof import('~icons/carbon/add.tsx')['default']
  const IconGridiconsFullscreen: typeof import('~icons/gridicons/fullscreen.tsx')['default']
  const IconGridiconsFullscreenExit: typeof import('~icons/gridicons/fullscreen-exit.tsx')['default']
  const IconIcRoundDelete: typeof import('~icons/ic/round-delete.tsx')['default']
  const IconIcRoundPlus: typeof import('~icons/ic/round-plus.tsx')['default']
  const IconIcRoundRefresh: typeof import('~icons/ic/round-refresh.tsx')['default']
  const IconIcRoundRemove: typeof import('~icons/ic/round-remove.tsx')['default']
  const IconIcRoundSearch: typeof import('~icons/ic/round-search.tsx')['default']
  const IconLocalBanner: typeof import('~icons/local/banner.tsx')['default']
  const IconLocalLogo: typeof import('~icons/local/logo.tsx')['default']
  const IconMdiArrowDownThin: typeof import('~icons/mdi/arrow-down-thin.tsx')['default']
  const IconMdiArrowUpThin: typeof import('~icons/mdi/arrow-up-thin.tsx')['default']
  const IconMdiDrag: typeof import('~icons/mdi/drag.tsx')['default']
  const IconMdiKeyboardEsc: typeof import('~icons/mdi/keyboard-esc.tsx')['default']
  const IconMdiKeyboardReturn: typeof import('~icons/mdi/keyboard-return.tsx')['default']
  const IconMdiRefresh: typeof import('~icons/mdi/refresh.tsx')['default']
  const IconUilSearch: typeof import('~icons/uil/search.tsx')['default']
  const IconWrapper: typeof import('../components/common/IconWrapper')['default']
  const IndexingScope: typeof import('../components/datasource/indexing_scope/index.jsx')['IndexingScope']
  const LangSwitch: typeof import('../components/stateful/LangSwitch')['default']
  const Licence: typeof import('../components/Licence/index.js')['default']
  const LicenceTrigger: typeof import('../components/Licence/LicenceTrigger.jsx')['default']
  const License: typeof import('../components/License/index.jsx')['default']
  const LicenseTrigger: typeof import('../components/License/LicenseTrigger.jsx')['default']
  const Link: typeof import('react-router-dom')['Link']
  const ListContainer: typeof import('../components/stateless/common/ListContainer')['default']
  const LocalFsSVG: typeof import('../components/icons/index.jsx')['LocalFsSVG']
  const LookForward: typeof import('../components/stateless/custom/LookForward')['default']
  const MenuContext: typeof import('../layouts/base-layout/MenuProvider')['MenuContext']
  const MenuProvider: typeof import('../layouts/base-layout/MenuProvider')['default']
  const MenuToggler: typeof import('../components/stateful/MenuToggler')['default']
  const MultiFilePathInput: typeof import('../components/datasource/type/file_paths.jsx')['MultiFilePathInput']
  const MultiURLInput: typeof import('../components/datasource/type/urls.jsx')['MultiURLInput']
  const NavLink: typeof import('react-router-dom')['NavLink']
  const Navigate: typeof import('react-router-dom')['Navigate']
  const NotionSVG: typeof import('../components/icons/index.jsx')['NotionSVG']
  const Outlet: typeof import('react-router-dom')['Outlet']
  const PinToggler: typeof import('../components/stateless/common/PinToggler')['default']
  const ReloadButton: typeof import('../components/stateless/common/ReloadButton')['default']
  const Route: typeof import('react-router-dom')['Route']
  const Routes: typeof import('react-router-dom')['Routes']
  const SearchSVG: typeof import('../components/icons/index.jsx')['SearchSVG']
  const SoybeanAvatar: typeof import('../components/stateful/SoybeanAvatar')['default']
  const SvgIcon: typeof import('../components/stateless/custom/SvgIcon')['default']
  const SystemLogo: typeof import('../components/stateless/common/SystemLogo')['default']
  const SystemLogoShort: typeof import('../components/stateless/common/SystemLogoShort')['default']
  const TableColumnSetting: typeof import('../components/advanced/TableColumnSetting')['default']
  const TableHeaderOperation: typeof import('../components/advanced/TableHeaderOperation')['default']
  const Tags: typeof import('../components/common/tags')['Tags']
  const ThemeSchemaSwitch: typeof import('../components/stateful/ThemeSchemaSwitch')['default']
  const TypeList: typeof import('../components/datasource/type/index.jsx')['TypeList']
  const TypeListComponent: typeof import('../components/datasource/type/index.jsx')['TypeListComponent']
  const Types: typeof import('../components/datasource/type/index.jsx')['Types']
  const Version: typeof import('../components/License/Version.jsx')['default']
  const WaveBg: typeof import('../components/stateless/custom/WaveBg')['default']
  const YuqueSVG: typeof import('../components/icons/index.jsx')['YuqueSVG']
  const bucket: typeof import('../components/icons/bucket.jsx')['default']
  const buttonRadio: typeof import('../components/button-radio/index')['default']
  const createRef: typeof import('react')['createRef']
  const fontIcon: typeof import('../components/common/font_icon.jsx')['default']
  const forwardRef: typeof import('react')['forwardRef']
  const gogoleDrive: typeof import('../components/icons/gogole_drive.jsx')['default']
  const googleDrive: typeof import('../components/icons/google_drive.jsx')['default']
  const hugo: typeof import('../components/icons/hugo.jsx')['default']
  const icon: typeof import('../components/common/icon.jsx')['default']
  const indexingScope: typeof import('../components/datasource/indexing_scope/index.jsx')['default']
  const lazy: typeof import('react')['lazy']
  const localFs: typeof import('../components/icons/local_fs.jsx')['default']
  const memo: typeof import('react')['memo']
  const notion: typeof import('../components/icons/notion.jsx')['default']
  const queryParams: typeof import('../hooks/common/queryParams')['default']
  const script: typeof import('../hooks/common/script')['default']
  const search: typeof import('../components/icons/search.jsx')['default']
  const startTransition: typeof import('react')['startTransition']
  const tags: typeof import('../components/common/tags')['default']
  const urls: typeof import('../components/datasource/type/urls.jsx')['default']
  const useAntdTable: typeof import('ahooks')['useAntdTable']
  const useAppDispatch: typeof import('../hooks/business/useStore')['useAppDispatch']
  const useAppSelector: typeof import('../hooks/business/useStore')['useAppSelector']
  const useAsyncEffect: typeof import('ahooks')['useAsyncEffect']
  const useAuth: typeof import('../hooks/business/auth')['useAuth']
  const useBoolean: typeof import('ahooks')['useBoolean']
  const useCallback: typeof import('react')['useCallback']
  const useCaptcha: typeof import('../hooks/business/captcha')['useCaptcha']
  const useClickAway: typeof import('ahooks')['useClickAway']
  const useContext: typeof import('react')['useContext']
  const useControllableValue: typeof import('ahooks')['useControllableValue']
  const useCookieState: typeof import('ahooks')['useCookieState']
  const useCountDown: typeof import('ahooks')['useCountDown']
  const useCounter: typeof import('ahooks')['useCounter']
  const useCreation: typeof import('ahooks')['useCreation']
  const useDebounce: typeof import('ahooks')['useDebounce']
  const useDebounceEffect: typeof import('ahooks')['useDebounceEffect']
  const useDebounceFn: typeof import('ahooks')['useDebounceFn']
  const useDebugValue: typeof import('react')['useDebugValue']
  const useDeepCompareEffect: typeof import('ahooks')['useDeepCompareEffect']
  const useDeepCompareLayoutEffect: typeof import('ahooks')['useDeepCompareLayoutEffect']
  const useDeferredValue: typeof import('react')['useDeferredValue']
  const useDocumentVisibility: typeof import('ahooks')['useDocumentVisibility']
  const useDrag: typeof import('ahooks')['useDrag']
  const useDrop: typeof import('ahooks')['useDrop']
  const useDynamicList: typeof import('ahooks')['useDynamicList']
  const useEcharts: typeof import('../hooks/common/echarts')['useEcharts']
  const useEffect: typeof import('react')['useEffect']
  const useEventEmitter: typeof import('ahooks')['useEventEmitter']
  const useEventListener: typeof import('ahooks')['useEventListener']
  const useEventTarget: typeof import('ahooks')['useEventTarget']
  const useExternal: typeof import('ahooks')['useExternal']
  const useFavicon: typeof import('ahooks')['useFavicon']
  const useFocusWithin: typeof import('ahooks')['useFocusWithin']
  const useFormRules: typeof import('../hooks/common/form')['useFormRules']
  const useFullscreen: typeof import('ahooks')['useFullscreen']
  const useFusionTable: typeof import('ahooks')['useFusionTable']
  const useGetState: typeof import('ahooks')['useGetState']
  const useHistoryTravel: typeof import('ahooks')['useHistoryTravel']
  const useHover: typeof import('ahooks')['useHover']
  const useHref: typeof import('react-router-dom')['useHref']
  const useIconfontScript: typeof import('../hooks/common/script')['useIconfontScript']
  const useId: typeof import('react')['useId']
  const useImperativeHandle: typeof import('react')['useImperativeHandle']
  const useInRouterContext: typeof import('react-router-dom')['useInRouterContext']
  const useInViewport: typeof import('ahooks')['useInViewport']
  const useInfiniteScroll: typeof import('ahooks')['useInfiniteScroll']
  const useInsertionEffect: typeof import('react')['useInsertionEffect']
  const useInterval: typeof import('ahooks')['useInterval']
  const useIsomorphicLayoutEffect: typeof import('ahooks')['useIsomorphicLayoutEffect']
  const useKeyPress: typeof import('ahooks')['useKeyPress']
  const useLatest: typeof import('ahooks')['useLatest']
  const useLayoutEffect: typeof import('react')['useLayoutEffect']
  const useLinkClickHandler: typeof import('react-router-dom')['useLinkClickHandler']
  const useLocalStorageState: typeof import('ahooks')['useLocalStorageState']
  const useLocation: typeof import('react-router-dom')['useLocation']
  const useLockFn: typeof import('ahooks')['useLockFn']
  const useLogin: typeof import('../hooks/common/login')['useLogin']
  const useLongPress: typeof import('ahooks')['useLongPress']
  const useMap: typeof import('ahooks')['useMap']
  const useMemo: typeof import('react')['useMemo']
  const useMemoizedFn: typeof import('ahooks')['useMemoizedFn']
  const useMenu: typeof import('../hooks/common/menu')['useMenu']
  const useMeta: typeof import('../hooks/common/meta')['useMeta']
  const useMixMenuContext: typeof import('../hooks/common/menu')['useMixMenuContext']
  const useMobile: typeof import('../hooks/common/mobile')['useMobile']
  const useMount: typeof import('ahooks')['useMount']
  const useMouse: typeof import('ahooks')['useMouse']
  const useMutationObserver: typeof import('ahooks')['useMutationObserver']
  const useNavigate: typeof import('react-router-dom')['useNavigate']
  const useNavigationType: typeof import('react-router-dom')['useNavigationType']
  const useNetwork: typeof import('ahooks')['useNetwork']
  const useOutlet: typeof import('react-router-dom')['useOutlet']
  const useOutletContext: typeof import('react-router-dom')['useOutletContext']
  const usePagination: typeof import('ahooks')['usePagination']
  const useParams: typeof import('react-router-dom')['useParams']
  const usePreferredColorScheme: typeof import('../hooks/business/usePreferredColorScheme')['default']
  const usePrevious: typeof import('ahooks')['usePrevious']
  const useRafInterval: typeof import('ahooks')['useRafInterval']
  const useRafState: typeof import('ahooks')['useRafState']
  const useRafTimeout: typeof import('ahooks')['useRafTimeout']
  const useReactive: typeof import('ahooks')['useReactive']
  const useReducer: typeof import('react')['useReducer']
  const useRef: typeof import('react')['useRef']
  const useRequest: typeof import('ahooks')['useRequest']
  const useResetState: typeof import('ahooks')['useResetState']
  const useResolvedPath: typeof import('react-router-dom')['useResolvedPath']
  const useResponsive: typeof import('ahooks')['useResponsive']
  const useRouterPush: typeof import('../hooks/common/routerPush')['useRouterPush']
  const useRoutes: typeof import('react-router-dom')['useRoutes']
  const useSafeState: typeof import('ahooks')['useSafeState']
  const useScroll: typeof import('ahooks')['useScroll']
  const useSearchParams: typeof import('react-router-dom')['useSearchParams']
  const useSelections: typeof import('ahooks')['useSelections']
  const useSessionStorageState: typeof import('ahooks')['useSessionStorageState']
  const useSet: typeof import('ahooks')['useSet']
  const useSetState: typeof import('ahooks')['useSetState']
  const useSize: typeof import('ahooks')['useSize']
  const useState: typeof import('react')['useState']
  const useSvgIcon: typeof import('../hooks/common/icon')['useSvgIcon']
  const useSyncExternalStore: typeof import('react')['useSyncExternalStore']
  const useTable: typeof import('../hooks/common/table')['useTable']
  const useTableOperate: typeof import('../hooks/common/table')['useTableOperate']
  const useTableScroll: typeof import('../hooks/common/table')['useTableScroll']
  const useTextSelection: typeof import('ahooks')['useTextSelection']
  const useThrottle: typeof import('ahooks')['useThrottle']
  const useThrottleEffect: typeof import('ahooks')['useThrottleEffect']
  const useThrottleFn: typeof import('ahooks')['useThrottleFn']
  const useTimeout: typeof import('ahooks')['useTimeout']
  const useTitle: typeof import('ahooks')['useTitle']
  const useToggle: typeof import('ahooks')['useToggle']
  const useTrackedEffect: typeof import('ahooks')['useTrackedEffect']
  const useTransition: typeof import('react')['useTransition']
  const useTranslation: typeof import('react-i18next')['useTranslation']
  const useUnmount: typeof import('ahooks')['useUnmount']
  const useUnmountedRef: typeof import('ahooks')['useUnmountedRef']
  const useUpdate: typeof import('ahooks')['useUpdate']
  const useUpdateEffect: typeof import('ahooks')['useUpdateEffect']
  const useUpdateLayoutEffect: typeof import('ahooks')['useUpdateLayoutEffect']
  const useVirtualList: typeof import('ahooks')['useVirtualList']
  const useWebSocket: typeof import('ahooks')['useWebSocket']
  const useWhyDidYouUpdate: typeof import('ahooks')['useWhyDidYouUpdate']
  const workTime: typeof import('../components/datasource/work_time/index.jsx')['default']
  const yuque: typeof import('../components/icons/yuque.jsx')['default']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { FC } from 'react'
  import('react')
}
