{"name": "react-soy", "type": "module", "version": "1.2.0", "private": false, "packageManager": "pnpm@9.12.3", "description": "A fresh and elegant admin template, based on React18、Vite5、TypeScript、Ant Design and UnoCSS. 一个基于React18、Vite5、TypeScript、Ant Design and UnoCSS的清新优雅的中后台模版。", "author": {"name": "INFINI Labs", "email": "<EMAIL>", "url": "https://github.com/infinilabs"}, "license": "MIT", "homepage": "https://github.com/infinilabs", "repository": {"url": "https://github.com/infinilabs/coco-server.git"}, "bugs": {"url": "https://github.com/infinilabs/coco-server/issues"}, "keywords": ["React admin", "react-admin-template", "Vite5", "TypeScript", "Ant Design", "antd-admin", "Redux", "React-Router V6", "UnoCSS"], "engines": {"node": ">=18.12.0", "pnpm": ">=8.7.0"}, "scripts": {"build": "vite build --mode prod", "build:test": "vite build --mode test", "cleanup": "sa cleanup", "commit": "sa git-commit", "commit:zh": "sa git-commit -l=zh-cn", "dev": "vite --mode test", "dev:prod": "vite --mode prod", "gen-route": "sa gen-route", "lint": "eslint . --fix", "preview": "vite preview", "release": "sa release", "typecheck": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "update-pkg": "sa update-pkg"}, "dependencies": {"@better-scroll/core": "2.5.1", "@iconify/react": "5.0.2", "@reduxjs/toolkit": "2.3.0", "@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/simple-router": "workspace:*", "@sa/utils": "workspace:*", "ahooks": "3.8.1", "antd": "5.21.6", "classnames": "2.5.1", "clipboard": "2.0.11", "dayjs": "1.11.13", "echarts": "5.5.1", "framer-motion": "11.11.11", "i18next": "23.16.4", "keepalive-for-react": "3.0.2", "lodash": "^4.17.21", "nprogress": "0.2.0", "query-string": "^9.2.1", "react": "18.3.1", "react-beautiful-dnd": "13.1.1", "react-countup": "6.5.3", "react-dom": "18.3.1", "react-error-boundary": "4.1.2", "react-hotkeys-hook": "^4.6.1", "react-i18next": "15.1.0", "react-redux": "9.1.2", "react-router-dom": "6.27.0", "react-svg": "^16.3.0", "typeit": "8.8.7"}, "devDependencies": {"@iconify/json": "2.2.266", "@iconify/types": "2.0.0", "@ohh-889/react-auto-route": "0.3.5", "@sa/scripts": "workspace:*", "@sa/uno-preset": "workspace:*", "@soybeanjs/eslint-config": "1.4.2", "@svgr/core": "8.1.0", "@svgr/plugin-jsx": "8.1.0", "@types/gradient-string": "1.1.6", "@types/node": "22.8.6", "@types/nprogress": "0.2.3", "@types/react": "18.3.12", "@types/react-beautiful-dnd": "13.1.8", "@types/react-dom": "18.3.1", "@types/react-transition-group": "4.4.11", "@typescript-eslint/eslint-plugin": "8.12.2", "@typescript-eslint/parser": "8.12.2", "@unocss/eslint-config": "0.63.6", "@unocss/preset-icons": "0.63.6", "@unocss/preset-uno": "0.63.6", "@unocss/transformer-directives": "0.63.6", "@unocss/transformer-variant-group": "0.63.6", "@unocss/vite": "0.63.6", "@vitejs/plugin-react": "4.3.3", "boxen": "8.0.1", "eslint": "9.14.0", "eslint-plugin-react": "7.37.2", "eslint-plugin-react-hooks": "5.0.0", "eslint-plugin-react-refresh": "0.4.14", "eslint-plugin-sort": "^4.0.0", "gradient-string": "3.0.0", "json5": "2.2.3", "less": "^4.3.0", "lint-staged": "15.2.10", "sass": "1.80.6", "tsx": "4.19.2", "typescript": "5.6.3", "unplugin-auto-import": "0.18.3", "unplugin-icons": "0.20.0", "vite": "5.4.10", "vite-plugin-inspect": "0.8.7", "vite-plugin-remove-console": "2.2.0", "vite-plugin-svg-icons": "2.0.1"}, "lint-staged": {"*": "eslint --fix"}, "website": "https://coco.rs/"}