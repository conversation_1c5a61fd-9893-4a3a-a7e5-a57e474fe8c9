## Usage

### Prerequisites

Ensure your environment meets the following requirements:

- **git**: For cloning and managing the project.
- **NodeJS**: >=18.12.0, recommended 18.19.0 or higher.
- **pnpm**: >= 8.7.0, recommended 8.14.0 or higher.

### Install Dependencies

```bash
pnpm i
```

> Since the project uses pnpm monorepo management, please do not use npm or yarn to install dependencies.

### Start Development Server

```bash
pnpm dev
```

### Build Project

```bash
pnpm build
```