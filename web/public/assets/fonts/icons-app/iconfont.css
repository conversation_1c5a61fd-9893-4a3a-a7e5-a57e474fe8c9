@font-face {
  font-family: "iconfont"; /* Project id 4878526 */
  src: url('iconfont.woff2?t=1752581377553') format('woff2'),
       url('iconfont.woff?t=1752581377553') format('woff'),
       url('iconfont.ttf?t=1752581377553') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.font_Qianfan:before {
  content: "\e746";
}

.font_VolcanoArk:before {
  content: "\e7c2";
}

.font_hunyuan:before {
  content: "\e611";
}

.font_Moonshot:before {
  content: "\e6bb";
}

.font_MiniMax:before {
  content: "\e7df";
}

.font_gemini-ai:before {
  content: "\e669";
}

.font_siliconflow:before {
  content: "\e6b1";
}

.font_Tools02:before {
  content: "\e884";
}

.font_tools01:before {
  content: "\e760";
}

.font_Efficiency:before {
  content: "\f9d1";
}

.font_help01:before {
  content: "\e60f";
}

.font_help02:before {
  content: "\e6df";
}

.font_docs:before {
  content: "\e7bd";
}

.font_Movie:before {
  content: "\e6b0";
}

.font_Record:before {
  content: "\ebd0";
}

.font_Ferry:before {
  content: "\e60e";
}

.font_Itinerary:before {
  content: "\e74b";
}

.font_Plane:before {
  content: "\e60d";
}

.font_railway:before {
  content: "\ea9f";
}

.font_Tickets:before {
  content: "\e60c";
}

.font_Patent:before {
  content: "\e62e";
}

.font_Share02:before {
  content: "\e630";
}

.font_Share01:before {
  content: "\e63b";
}

.font_Writing:before {
  content: "\e615";
}

.font_Brain02:before {
  content: "\e766";
}

.font_web:before {
  content: "\e667";
}

.font_Brain01:before {
  content: "\e6f1";
}

.font_Organization:before {
  content: "\e66b";
}

.font_Inspiration:before {
  content: "\e654";
}

.font_Laboratory:before {
  content: "\e745";
}

.font_Palette:before {
  content: "\e62d";
}

.font_chat01:before {
  content: "\e61b";
}

.font_Law:before {
  content: "\e6b4";
}

.font_chat02:before {
  content: "\e61f";
}

.font_Warning:before {
  content: "\e60a";
}

.font_code:before {
  content: "\e71b";
}

.font_Translation:before {
  content: "\e653";
}

.font_Review:before {
  content: "\fe14";
}

.font_Chart:before {
  content: "\e692";
}

.font_Translation02:before {
  content: "\e744";
}

.font_email:before {
  content: "\e61e";
}

.font_Search01:before {
  content: "\e65d";
}

.font_Cluster:before {
  content: "\e65e";
}

.font_Invoice:before {
  content: "\e65f";
}

.font_app02:before {
  content: "\e660";
}

.font_todolist:before {
  content: "\e661";
}

.font_Member:before {
  content: "\e662";
}

.font_House:before {
  content: "\e663";
}

.font_gcp:before {
  content: "\e613";
}

.font_infinilabs:before {
  content: "\e6d9";
}

.font_txunyun:before {
  content: "\e6dc";
}

.font_aliyun:before {
  content: "\e6de";
}

.font_INFINICloud-icon:before {
  content: "\e71a";
}

.font_Pizza:before {
  content: "\e71c";
}

.font_blogs:before {
  content: "\e608";
}

.font_AgentQL:before {
  content: "\e743";
}

.font_perplexity:before {
  content: "\e6bc";
}

.font_mcp:before {
  content: "\e6e1";
}

.font_aws:before {
  content: "\e61a";
}

.font_baiduditu:before {
  content: "\e607";
}

.font_gitlab:before {
  content: "\e65c";
}

.font_Slack:before {
  content: "\e606";
}

.font_Brave:before {
  content: "\e643";
}

.font_duckduckgo-white:before {
  content: "\e602";
}

.font_gaodeditu:before {
  content: "\e618";
}

.font_tengxunditu:before {
  content: "\e619";
}

.font_deepseek:before {
  content: "\e601";
}

.font_coco:before {
  content: "\e742";
}

.font_robot:before {
  content: "\e741";
}

.font_coco-logo-line:before {
  content: "\e740";
}

.font_Robot-outlined:before {
  content: "\e708";
}

.font_gitee:before {
  content: "\e73f";
}

.font_openai:before {
  content: "\e645";
}

.font_openai1:before {
  content: "\e605";
}

.font_tongyiqianwenTongyi-Qianwen:before {
  content: "\e63d";
}

.font_Google-pdf:before {
  content: "\e73e";
}

.font_page:before {
  content: "\e735";
}

.font_database:before {
  content: "\e73d";
}

.font_Google-script:before {
  content: "\e731";
}

.font_Google-photo:before {
  content: "\e732";
}

.font_Google-ms-excel:before {
  content: "\e733";
}

.font_Google-ms-word:before {
  content: "\e734";
}

.font_Google-drawing:before {
  content: "\e736";
}

.font_Google-form:before {
  content: "\e737";
}

.font_Google-map:before {
  content: "\e738";
}

.font_Google-fusiontable:before {
  content: "\e739";
}

.font_Google-audio:before {
  content: "\e73b";
}

.font_Google-document:before {
  content: "\e73c";
}

.font_Google-zip:before {
  content: "\e72b";
}

.font_Google-video:before {
  content: "\e72c";
}

.font_Google-ms-powerpoint:before {
  content: "\e72d";
}

.font_Google-presentation:before {
  content: "\e72e";
}

.font_Google-spreadsheet:before {
  content: "\e72f";
}

.font_Google-site:before {
  content: "\e730";
}

.font_Googledrive-folder:before {
  content: "\e80c";
}

.font_yuque-directory:before {
  content: "\e72a";
}

.font_yuque-sheet:before {
  content: "\e724";
}

.font_yuque-folder:before {
  content: "\e725";
}

.font_yuque-table:before {
  content: "\e726";
}

.font_yuque-book:before {
  content: "\e727";
}

.font_yuque-board:before {
  content: "\e728";
}

.font_yuque-doc:before {
  content: "\e729";
}

.font_hugo-news:before {
  content: "\e71f";
}

.font_hugo-web:before {
  content: "\e721";
}

.font_hugo-webpage:before {
  content: "\e722";
}

.font_hugo-blog:before {
  content: "\e723";
}

.font_GitHub:before {
  content: "\ea0a";
}

.font_simplenote:before {
  content: "\e9b7";
}

.font_youdao:before {
  content: "\e603";
}

.font_icon-test:before {
  content: "\e600";
}

.font_onenote:before {
  content: "\e621";
}

.font_personal-storage:before {
  content: "\e668";
}

.font_filetype-txt:before {
  content: "\e6a4";
}

.font_filetype-xls:before {
  content: "\e6a5";
}

.font_filetype-ppt:before {
  content: "\e6a6";
}

.font_filetype-video:before {
  content: "\e6a7";
}

.font_filetype-folder:before {
  content: "\e6a8";
}

.font_filetype-img:before {
  content: "\e6a9";
}

.font_filetype-link:before {
  content: "\e6ac";
}

.font_filetype-PDF:before {
  content: "\e6ad";
}

.font_filetype-word:before {
  content: "\e6ae";
}

.font_filetype-sound:before {
  content: "\e6af";
}

.font_shimodoc:before {
  content: "\e604";
}

.font_Googledrive:before {
  content: "\e609";
}

.font_bucket:before {
  content: "\e9ac";
}

.font_tengxundoc:before {
  content: "\e6ed";
}

.font_yinxiangbiji:before {
  content: "\e610";
}

.font_jinshandoc:before {
  content: "\e7c0";
}

.font_icon-feishu:before {
  content: "\e60b";
}

.font_yuque:before {
  content: "\e720";
}

.font_obsidian:before {
  content: "\e677";
}

.font_icon-book:before {
  content: "\e614";
}

.font_icon-book2:before {
  content: "\e616";
}

.font_icon-robot:before {
  content: "\e617";
}

.font_hugo:before {
  content: "\e73a";
}

.font_mysql:before {
  content: "\e636";
}

.font_windows:before {
  content: "\e62c";
}

.font_ollama:before {
  content: "\e675";
}

