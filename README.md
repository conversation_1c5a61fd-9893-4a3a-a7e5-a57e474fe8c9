# Coco AI Server - **Co**nnect & **Co**llaborate

Coco Server is a core component of the **Coco AI project**. To explore more about Coco AI, visit: [https://coco.rs/](https://coco.rs/).

As the **heart of Unified Collaboration**, Coco Server forms the backbone of your connected workspace, seamlessly integrating and managing enterprise applications, data, and workflows.

### Key Features

- **Unified Search**: Instantly locate anything across Google Workspace, Dropbox, Confluence, GitHub, and more.
- **Enhanced Collaboration**: Empower team communication and innovation with centralized, easily accessible knowledge.
- **Gen-AI Insights**: Harness AI to unlock your team’s unique expertise, delivering actionable insights and smarter decisions.
- **Effortless Integration**: Built for simple setup, enterprise-grade security, and unmatched scalability.
- **Easy to embedding**: Easily embedding the Coco AI capacities into your website within 1 minute.

### Architecture

Coco AI is designed for extensibility.

![Coco Server Architecture](https://docs.infinilabs.com/coco-server/main/img/coco-server-architecture.png)

### Management Console

Coco AI is designed for ease of use.

![Coco Server Home](https://docs.infinilabs.com/coco-server/main/img/home.png)

For more details on Coco Server, visit: 

- [Coco App Documentation](https://docs.infinilabs.com/coco-app/main/)
- [Coco Server Documentation](https://docs.infinilabs.com/coco-server/main/)
- [DeepWiki Coco App](https://deepwiki.com/infinilabs/coco-app)
- [DeepWiki Coco Server](https://deepwiki.com/infinilabs/coco-server)

## Community

Feel free to join the Discord server to discuss anything related to this project:

👉 [Join the INFINI Community on Discord](https://discord.gg/4tKTMkkvVX)

## Contributors

<a href="https://github.com/infinilabs/coco-server/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=infinilabs/coco-server" />
</a>

## License

Coco Server is a truly open-source project, licensed under the [GNU Affero General Public License v3.0](https://opensource.org/licenses/AGPL-3.0).

Unlike restrictive licenses, Coco Server grants you full freedom to:
- Host it anywhere – No restrictions on self-hosting or cloud deployment.
- Run multi-tenant services – No need for special permission to serve multiple customers.
- Modify and rebrand freely – Customize the UI, change logos, and adapt it to your needs, in full compliance with AGPL-3.0.
- Use it for any purpose – Personal, commercial, or enterprise use, all without additional licensing requirements.

For those who need enterprise-grade support, we also offer a commercially supported version.
For more details, refer to our [license information](./LICENSE).
